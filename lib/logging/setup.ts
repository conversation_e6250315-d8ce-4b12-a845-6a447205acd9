import { setupLogging, LogLevel } from "./index"

export function initializeLogging() {
  const logger = setupLogging({
    level: (process.env.LOG_LEVEL as LogLevel) || LogLevel.INFO,
    enableConsole: true,
    enableStackTrace:
      process.env.NODE_ENV === "development" ||
      process.env.ENABLE_STACK_TRACE === "true",
    defaultMetadata: {
      service: "cs-ai-app-" + process.env.APP_ENVIRONMENT,
      environment: process.env.NODE_ENV || "development",
      version: process.env.npm_package_version || "1.0.0",
      hostname: process.env.HOSTNAME || "unknown",
    },
  })

  // Log initialization
  logger.info("Logging system initialized", {
    level: process.env.LOG_LEVEL || LogLevel.INFO,
    grafanaEnabled: !!process.env.GRAFANA_LOKI_ENDPOINT,
    consoleEnabled: true,
    environment: process.env.NODE_ENV || "development",
  })

  return logger
}
