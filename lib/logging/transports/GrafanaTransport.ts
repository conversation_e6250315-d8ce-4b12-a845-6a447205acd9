import { LogTransport, LogEntry, GrafanaLogConfig } from "../types"

export class GrafanaTransport implements LogTransport {
  public readonly name = "grafana"
  private config: GrafanaLogConfig
  private logBuffer: LogEntry[] = []
  private flushTimer?: NodeJS.Timeout

  constructor(config: GrafanaLogConfig) {
    this.config = {
      batchSize: 100,
      flushInterval: 5000, // 5 seconds
      retryAttempts: 3,
      retryDelay: 1000,
      datasource: "loki",
      ...config,
    }

    // Start flush timer
    this.startFlushTimer()
  }

  async send(entry: LogEntry): Promise<void> {
    this.logBuffer.push(entry)

    // Flush immediately if buffer is full
    if (this.logBuffer.length >= this.config.batchSize!) {
      await this.flush()
    }
  }

  private startFlushTimer(): void {
    this.flushTimer = setInterval(() => {
      if (this.logBuffer.length > 0) {
        this.flush().catch(console.error)
      }
    }, this.config.flushInterval)
  }

  private async flush(): Promise<void> {
    if (this.logBuffer.length === 0) return

    const logsToSend = [...this.logBuffer]
    this.logBuffer = []

    try {
      await this.sendToGrafana(logsToSend)
    } catch (error) {
      console.error("Failed to send logs to Grafana:", error)
      // Put logs back in buffer for retry
      this.logBuffer.unshift(...logsToSend)
    }
  }

  private async sendToGrafana(logs: LogEntry[]): Promise<void> {
    const streams = this.formatForLoki(logs)

    const payload = {
      streams,
    }

    const headers: Record<string, string> = {
      "Content-Type": "application/json",
    }

    // Add authentication
    if (this.config.apiKey) {
      headers["Authorization"] = `Bearer ${this.config.apiKey}`
    } else if (this.config.username && this.config.password) {
      const auth = Buffer.from(
        `${this.config.username}:${this.config.password}`,
      ).toString("base64")
      headers["Authorization"] = `Basic ${auth}`
    }

    let attempt = 0
    while (attempt < this.config.retryAttempts!) {
      try {
        const response = await fetch(
          `${this.config.endpoint}/loki/api/v1/push`,
          {
            method: "POST",
            headers,
            body: JSON.stringify(payload),
          },
        )

        if (!response.ok) {
          throw new Error(
            `Grafana API error: ${response.status} ${response.statusText}`,
          )
        }

        return // Success
      } catch (error) {
        attempt++
        if (attempt >= this.config.retryAttempts!) {
          throw error
        }

        // Wait before retry
        await new Promise((resolve) =>
          setTimeout(resolve, this.config.retryDelay! * attempt),
        )
      }
    }
  }

  private formatForLoki(logs: LogEntry[]): any[] {
    // Group logs by labels for efficient streaming
    const streamMap = new Map<
      string,
      { stream: Record<string, string>; values: [string, string][] }
    >()

    for (const log of logs) {
      const labels = this.buildLabels(log)
      const labelKey = JSON.stringify(labels)

      if (!streamMap.has(labelKey)) {
        streamMap.set(labelKey, {
          stream: labels,
          values: [],
        })
      }

      const stream = streamMap.get(labelKey)!
      const timestamp = new Date(log.timestamp).getTime() * 1000000 // Convert to nanoseconds
      const logLine = this.formatLogLine(log)

      stream.values.push([timestamp.toString(), logLine])
    }

    return Array.from(streamMap.values())
  }

  private buildLabels(log: LogEntry): Record<string, string> {
    const labels: Record<string, string> = {
      level: log.level,
      service: "cs-ai-app-" + process.env.APP_ENVIRONMENT,
      ...this.config.labels,
    }

    // Add metadata as labels (only string values)
    if (log.metadata) {
      for (const [key, value] of Object.entries(log.metadata)) {
        if (typeof value === "string" && key !== "message") {
          labels[key] = value
        }
      }
    }

    return labels
  }

  private formatLogLine(log: LogEntry): string {
    const logData = {
      message: log.message,
      timestamp: log.timestamp,
      level: log.level,
      metadata: log.metadata,
      error: log.error,
      source: log.source,
    }

    return JSON.stringify(logData)
  }

  public async destroy(): Promise<void> {
    if (this.flushTimer) {
      clearInterval(this.flushTimer)
    }

    // Flush remaining logs
    await this.flush()
  }
}
