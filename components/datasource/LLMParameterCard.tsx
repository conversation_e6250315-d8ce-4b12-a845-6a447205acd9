"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Trash2, Bot } from "lucide-react"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "./locales"
import { LLMParameter } from "@/lib/repositories/datasources/interface"

interface LLMParameterCardProps {
  llmParam: LLMParameter
  index: number
  onRemoveLLMParameter: (index: number) => void
  onUpdateLLMParameter: (index: number, updates: Partial<LLMParameter>) => void
}

export function LLMParameterCard({
  llmParam,
  index,
  onRemoveLLMParameter,
  onUpdateLLMParameter,
}: LLMParameterCardProps) {
  const { t } = useLocalization("datasource", locales)

  return (
    <div className="border rounded-lg p-4 space-y-3 bg-blue-50 border-blue-200">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Bot className="h-4 w-4 text-blue-600" />
          <h4 className="font-medium text-blue-800">
            LLM Parameter {index + 1}
          </h4>
        </div>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => onRemoveLLMParameter(index)}
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>

      <div className="grid grid-cols-2 gap-3">
        <div>
          <Label>{t("form.tool.variable_name")}</Label>
          <Input
            value={llmParam.name}
            onChange={(e) =>
              onUpdateLLMParameter(index, {
                name: e.target.value,
              })
            }
            placeholder="order_number"
          />
        </div>
        <div>
          <Label>Type</Label>
          <Select
            value={llmParam.type}
            onValueChange={(value) =>
              onUpdateLLMParameter(index, { type: value as any })
            }
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="string">String</SelectItem>
              <SelectItem value="number">Number</SelectItem>
              <SelectItem value="boolean">Boolean</SelectItem>
              <SelectItem value="object">Object</SelectItem>
              <SelectItem value="array">Array</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div>
        <Label>Description</Label>
        <Textarea
          value={llmParam.description}
          onChange={(e) =>
            onUpdateLLMParameter(index, {
              description: e.target.value,
            })
          }
          placeholder="Describe what this parameter represents..."
          className="min-h-16"
        />
      </div>

      <div>
        <Label>{t("form.tool.llm_prompt")}</Label>
        <Textarea
          value={llmParam.prompt || ""}
          onChange={(e) =>
            onUpdateLLMParameter(index, {
              prompt: e.target.value,
            })
          }
          placeholder={t("form.tool.llm_prompt_placeholder")}
          className="min-h-20"
        />
      </div>
    </div>
  )
}
