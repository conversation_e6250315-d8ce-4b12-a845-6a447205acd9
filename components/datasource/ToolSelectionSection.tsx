"use client"

import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Database,
  Table,
  FileSpreadsheet,
  Settings,
} from "lucide-react"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "./locales"
import { DatasourceFormData } from "./DatasourceFormBase"
import { AvailableTool } from "@/lib/services/datasourcesApi"

interface ToolSelectionSectionProps {
  formData: DatasourceFormData
  availableTools: AvailableTool[]
  loadingTools: boolean
  isCustomTool: boolean
  onToolSelection: (toolId: string) => void
  onUpdateToolConfig: (updates: Partial<NonNullable<DatasourceFormData["toolCallConfig"]>>) => void
  onUpdateFormData: (updates: Partial<DatasourceFormData>) => void
}

export function ToolSelectionSection({
  formData,
  availableTools,
  loadingTools,
  isCustomTool,
  onToolSelection,
  onUpdateToolConfig,
  onUpdateFormData,
}: ToolSelectionSectionProps) {
  const { t } = useLocalization("datasource", locales)

  // Helper function to get icon for tool type
  const getToolIcon = (toolId: string) => {
    switch (toolId) {
      case "google_sheets_reader":
        return FileSpreadsheet
      case "mongodb_reader":
        return Database
      case "postgresql_reader":
        return Table
      case "api_caller":
        return Settings
      default:
        return Settings
    }
  }

  return (
    <div className="space-y-4">
      {/* Query Field */}
      <div className="space-y-2">
        <Label htmlFor="query">{t("form.tool.query_label")}</Label>
        <Textarea
          id="query"
          value={formData.query || ""}
          onChange={(e) => onUpdateFormData({ query: e.target.value })}
          placeholder={t("form.tool.query_placeholder")}
          className="min-h-20"
          maxLength={1000}
          required
        />
        <p className="text-sm text-gray-500">
          {(formData.query || "").length}/1,000{" "}
          {t("form.common.characters")}
        </p>
      </div>

      {/* Tool Name */}
      <div className="space-y-2">
        <Label htmlFor="toolId">{t("form.tool.tool_name_label")}</Label>
        {loadingTools ? (
          <div className="flex items-center justify-center p-3 border rounded-md">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900"></div>
            <span className="ml-2 text-sm text-gray-500">
              {t("form.tool.loading_tools")}
            </span>
          </div>
        ) : (
          <>
            <Select
              value={
                isCustomTool
                  ? "custom"
                  : formData.toolCallConfig?.toolId || ""
              }
              onValueChange={onToolSelection}
            >
              <SelectTrigger>
                <SelectValue
                  placeholder={t("form.tool.tool_name_placeholder")}
                />
              </SelectTrigger>
              <SelectContent>
                {availableTools.map((tool) => {
                  const IconComponent = getToolIcon(tool.id)
                  return (
                    <SelectItem key={tool.id} value={tool.id}>
                      <div className="flex items-center gap-2">
                        <IconComponent className="h-4 w-4 text-gray-600" />
                        <div className="flex flex-col">
                          <span className="font-medium">{tool.name}</span>
                          <span className="text-xs text-gray-500">
                            {tool.description}
                          </span>
                        </div>
                      </div>
                    </SelectItem>
                  )
                })}
                <SelectItem value="custom">
                  <div className="flex flex-col">
                    <span className="font-medium">
                      {t("form.tool.custom_tool_option")}
                    </span>
                    <span className="text-xs text-gray-500">
                      {t("form.tool.custom_tool_description")}
                    </span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>

            {isCustomTool && (
              <Input
                value={formData.toolCallConfig?.toolId || ""}
                onChange={(e) =>
                  onUpdateToolConfig({ toolId: e.target.value })
                }
                placeholder={t("form.tool.tool_name_custom_placeholder")}
                className="mt-2"
                required
              />
            )}

            {!isCustomTool && formData.toolCallConfig?.toolId && (
              <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-md">
                <p className="text-sm text-blue-800">
                  <strong>{t("form.tool.prebuilt_tool_selected")}</strong>
                </p>
              </div>
            )}
          </>
        )}
      </div>

      {/* Tool Description */}
      <div className="space-y-2">
        <Label htmlFor="toolDescription">
          {t("form.tool.tool_description_label")}
          {!isCustomTool && formData.toolCallConfig?.toolId && (
            <span className="text-xs text-gray-500 ml-2">
              {t("form.tool.auto_populated_description")}
            </span>
          )}
        </Label>
        <Textarea
          id="toolDescription"
          value={formData.toolCallConfig?.description || ""}
          onChange={(e) =>
            onUpdateToolConfig({ description: e.target.value })
          }
          placeholder={t("form.tool.tool_description_placeholder")}
          className="min-h-20"
          required
        />
      </div>
    </div>
  )
}
