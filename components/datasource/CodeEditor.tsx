"use client"

import React, { useEffect, useRef } from "react"
import { EditorContent, useEditor } from "@tiptap/react"
import { StarterKit } from "@tiptap/starter-kit"
import { CodeBlockLowlight } from "@tiptap/extension-code-block-lowlight"
import { createLowlight } from "lowlight"
import { cn } from "@/lib/utils"

// Create lowlight instance
const lowlight = createLowlight()

interface CodeEditorProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  className?: string
  language?: string
  disabled?: boolean
}

export function CodeEditor({
  value,
  onChange,
  placeholder = "Enter JSON or code...",
  className,
  language = "json",
  disabled = false,
}: CodeEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null)

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        codeBlock: false, // Disable default code block
      }),
      CodeBlockLowlight.configure({
        lowlight,
        defaultLanguage: language,
      }),
    ],
    content: value
      ? `<pre><code class="language-${language}">${value}</code></pre>`
      : "",
    editorProps: {
      attributes: {
        class: cn(
          "prose prose-sm max-w-none focus:outline-none",
          "min-h-[120px] p-3 border rounded-md",
          "font-mono text-sm leading-relaxed",
          disabled && "opacity-50 cursor-not-allowed",
          className,
        ),
        placeholder,
      },
    },
    editable: !disabled,
    onUpdate: ({ editor }) => {
      // Extract text content from the code block
      const content = editor.getText()
      onChange(content)
    },
    onCreate: ({ editor }) => {
      // If we have initial value, set it properly
      if (value) {
        editor.commands.setContent(
          `<pre><code class="language-${language}">${value}</code></pre>`,
        )
      } else {
        // Create an empty code block
        editor.commands.setContent(
          `<pre><code class="language-${language}"></code></pre>`,
        )
      }
    },
  })

  // Update editor content when value prop changes
  useEffect(() => {
    if (editor && value !== editor.getText()) {
      const currentContent = `<pre><code class="language-${language}">${value}</code></pre>`
      editor.commands.setContent(currentContent)
    }
  }, [value, editor, language])

  // Focus the editor when clicked
  const handleClick = () => {
    if (editor && !disabled) {
      editor.commands.focus()
    }
  }

  return (
    <div
      ref={editorRef}
      onClick={handleClick}
      className={cn(
        "relative border rounded-md overflow-hidden",
        "bg-gray-50 dark:bg-gray-900",
        disabled && "opacity-50 cursor-not-allowed",
        className,
      )}
    >
      <EditorContent
        editor={editor}
        className={cn(
          "code-editor-content",
          "[&_.ProseMirror]:min-h-[120px]",
          "[&_.ProseMirror]:p-3",
          "[&_.ProseMirror]:font-mono",
          "[&_.ProseMirror]:text-sm",
          "[&_.ProseMirror]:leading-relaxed",
          "[&_.ProseMirror]:focus:outline-none",
          "[&_pre]:m-0",
          "[&_pre]:p-0",
          "[&_pre]:bg-transparent",
          "[&_pre]:border-none",
          "[&_code]:bg-transparent",
          "[&_code]:p-0",
          "[&_code]:border-none",
          "[&_code]:font-mono",
          "[&_code]:text-sm",
        )}
      />
      {!value && (
        <div className="absolute top-3 left-3 pointer-events-none text-gray-400 font-mono text-sm">
          {placeholder}
        </div>
      )}
    </div>
  )
}
