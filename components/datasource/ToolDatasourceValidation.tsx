import { DatasourceFormData } from "./DatasourceFormBase"

export function validateToolDatasource(formData: DatasourceFormData): boolean {
  const basicValidation = !!(
    formData.name.trim() &&
    formData.query?.trim() &&
    formData.toolCallConfig?.toolId?.trim() &&
    formData.toolCallConfig?.description?.trim()
  )

  if (!basicValidation) return false

  // Validate LLM parameter mappings
  const toolParams = formData.toolCallConfig?.parameters || []
  const llmParams = formData.toolCallConfig?.llmParameters || []
  const llmParamNames = llmParams.map((p) => p.name)

  // Check that all LLM-filled parameters have valid LLM variable mappings
  for (const param of toolParams) {
    const isLLMFilled = param.value && llmParamNames.includes(param.value)

    if (isLLMFilled && !llmParamNames.includes(param.value!)) {
      return false // LLM variable doesn't exist
    }

    // Check that required parameters have values (either direct or LLM)
    if (param.required && !param.value?.trim()) {
      return false // Required parameter has no value
    }
  }

  // Check that all LLM parameters have names and descriptions
  for (const llmParam of llmParams) {
    if (!llmParam.name?.trim() || !llmParam.description?.trim()) {
      return false // LLM parameter missing required fields
    }

    // Ensure LLM parameter names are not empty strings
    if (llmParam.name.trim() === "") {
      return false // LLM parameter name cannot be empty
    }
  }

  return true
}
