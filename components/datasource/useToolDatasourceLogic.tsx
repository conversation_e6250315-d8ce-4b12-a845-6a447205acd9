import { useState, useEffect } from "react"
import { DatasourceFormData } from "./DatasourceFormBase"
import { DatasourcesAPI, AvailableTool } from "@/lib/services/datasourcesApi"
import { LLMParameter } from "@/lib/repositories/datasources/interface"

export function useToolDatasourceLogic(
  formData: DatasourceFormData,
  onFormDataChange: (data: DatasourceFormData) => void,
  datasourceId?: string,
) {
  const [showTestDialog, setShowTestDialog] = useState(false)
  const [availableTools, setAvailableTools] = useState<AvailableTool[]>([])
  const [loadingTools, setLoadingTools] = useState(false)
  const [isCustomTool, setIsCustomTool] = useState(false)

  // Fetch available tools on component mount
  useEffect(() => {
    const fetchAvailableTools = async () => {
      try {
        setLoadingTools(true)
        const response = await DatasourcesAPI.GetAvailableTools().request()
        setAvailableTools(response)
        console.log("TOOLNAME", formData.toolCallConfig?.toolId, response)
        // Check if current tool is a custom tool (not in prebuilt list)
        if (formData.toolCallConfig?.toolId) {
          const isPrebuilt = response.some(
            (tool) => tool.id === formData.toolCallConfig?.toolId,
          )
          setIsCustomTool(!isPrebuilt)
        }
      } catch (error) {
        console.error("Failed to fetch available tools:", error)
      } finally {
        setLoadingTools(false)
      }
    }

    fetchAvailableTools()
  }, [])

  const updateFormData = (updates: Partial<DatasourceFormData>) => {
    onFormDataChange({ ...formData, ...updates })
  }

  const updateToolConfig = (
    updates: Partial<NonNullable<DatasourceFormData["toolCallConfig"]>>,
  ) => {
    updateFormData({
      toolCallConfig: {
        ...formData.toolCallConfig!,
        ...updates,
      },
    })
  }

  // Handle tool selection and update parameters template
  const handleToolSelection = (toolId: string) => {
    if (toolId === "custom") {
      setIsCustomTool(true)
      updateToolConfig({
        toolId: "",
        parameters: [],
        llmParameters: [],
      })
      return
    }

    setIsCustomTool(false)
    const selectedTool = availableTools.find((tool) => tool.id === toolId)

    if (selectedTool) {
      // Convert tool parameters to form parameters
      const formParameters = selectedTool.parameters.map((param) => ({
        name: param.name,
        type: param.type,
        required: param.required,
        description: param.description || "",
        value: "",
      }))

      updateToolConfig({
        toolId: selectedTool.id,
        description: selectedTool.description,
        parameters: formParameters,
        llmParameters: [],
      })
    }
  }

  // LLM Parameter management functions
  const addLLMParameter = () => {
    const newLLMParameter: LLMParameter = {
      name: "",
      type: "string",
      description: "",
      prompt: "",
    }

    updateToolConfig({
      llmParameters: [
        ...(formData.toolCallConfig?.llmParameters || []),
        newLLMParameter,
      ],
    })
  }

  const removeLLMParameter = (index: number) => {
    const llmParameters = [...(formData.toolCallConfig?.llmParameters || [])]
    llmParameters.splice(index, 1)
    updateToolConfig({ llmParameters })
  }

  const updateLLMParameter = (
    index: number,
    updates: Partial<LLMParameter>,
  ) => {
    const llmParameters = [...(formData.toolCallConfig?.llmParameters || [])]
    llmParameters[index] = { ...llmParameters[index], ...updates }
    updateToolConfig({ llmParameters })
  }

  // Tool Parameter management functions (updated to handle LLM mapping)
  const updateToolParameter = (
    index: number,
    updates: Partial<
      NonNullable<DatasourceFormData["toolCallConfig"]>["parameters"][0]
    >,
  ) => {
    const parameters = [...(formData.toolCallConfig?.parameters || [])]
    parameters[index] = { ...parameters[index], ...updates }
    updateToolConfig({ parameters })
  }

  const updateParameterValue = (paramIndex: number, value: string) => {
    updateToolParameter(paramIndex, {
      value: value === "direct_input" ? "" : value,
    })
  }

  // Helper function to check if a parameter is LLM-filled
  const isParameterLLMFilled = (param: any) => {
    return (
      param.value &&
      (formData.toolCallConfig?.llmParameters || []).some(
        (llmParam) => llmParam.name === param.value,
      )
    )
  }

  const addParameter = () => {
    updateToolConfig({
      parameters: [
        ...(formData.toolCallConfig?.parameters || []),
        {
          name: "",
          type: "string",
          description: "",
          required: false,
          value: "",
        },
      ],
    })
  }

  const removeParameter = (index: number) => {
    updateToolConfig({
      parameters:
        formData.toolCallConfig?.parameters?.filter((_, i) => i !== index) ||
        [],
    })
  }

  const canTest =
    datasourceId &&
    formData.toolCallConfig?.toolId?.trim() &&
    formData.toolCallConfig?.description?.trim()

  const handleTestTool = () => {
    setShowTestDialog(true)
  }

  return {
    // State
    showTestDialog,
    setShowTestDialog,
    availableTools,
    loadingTools,
    isCustomTool,

    // Functions
    updateFormData,
    updateToolConfig,
    handleToolSelection,
    addLLMParameter,
    removeLLMParameter,
    updateLLMParameter,
    updateParameterValue,
    isParameterLLMFilled,
    addParameter,
    removeParameter,
    canTest,
    handleTestTool,
  }
}
