"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Trash2, <PERSON><PERSON>, ArrowRight } from "lucide-react"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "./locales"
import { DatasourceFormData } from "./DatasourceFormBase"
import { CodeEditor } from "./CodeEditor"

interface ToolParameterCardProps {
  param: NonNullable<DatasourceFormData["toolCallConfig"]>["parameters"][0]
  index: number
  formData: DatasourceFormData
  onRemoveParameter: (index: number) => void
  onUpdateParameterValue: (index: number, value: string) => void
  isParameterLLMFilled: (param: any) => boolean
}

export function ToolParameterCard({
  param,
  index,
  formData,
  onRemoveParameter,
  onUpdateParameterValue,
  isParameterLLMFilled,
}: ToolParameterCardProps) {
  const { t } = useLocalization("datasource", locales)

  return (
    <div className="border rounded-lg p-4 space-y-3">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <h4 className="font-medium">
            {param.name || `Parameter ${index + 1}`}
          </h4>
          {param.required && (
            <span className="px-2 py-1 bg-red-100 text-red-800 rounded-md text-xs font-medium">
              Required
            </span>
          )}
          {param.enum && param.enum.length > 0 && (
            <span className="px-2 py-1 bg-purple-100 text-purple-800 rounded-md text-xs font-medium">
              Enum
            </span>
          )}
          {isParameterLLMFilled(param) && (
            <div className="flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 rounded-md text-xs">
              <Bot className="h-3 w-3" />
              LLM
            </div>
          )}
        </div>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={() => onRemoveParameter(index)}
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>

      <div className="grid grid-cols-2 gap-3">
        <div>
          <Label className="text-sm font-medium text-gray-700">
            {t("form.tool.variable_name")}
          </Label>
          <p className="text-sm text-gray-900 font-mono px-3 py-2">
            {param.name}
          </p>
        </div>
        <div>
          <Label className="text-sm font-medium text-gray-700">
            {t("form.tool.type")}
          </Label>
          <p className="text-sm text-gray-900 capitalize px-3 py-2">
            {param.type}
          </p>
        </div>
      </div>

      <div>
        <Label className="text-sm font-medium text-gray-700">
          {t("form.tool.description")}
        </Label>
        <p className="text-sm text-gray-900 px-3 py-2 min-h-16 whitespace-pre-wrap">
          {param.description || "No description provided"}
        </p>
        {param.enum && param.enum.length > 0 && (
          <p className="text-xs text-purple-600 mt-1">
            Available values: {param.enum.join(", ")}
          </p>
        )}
      </div>

      <div>
        <div className="flex items-center gap-2">
          <Label>{t("form.tool.parameter_value")}</Label>
          {param.required && !param.value?.trim() && (
            <span className="text-xs text-red-600 font-medium">
              ⚠️ Required field is empty
            </span>
          )}
        </div>
        
        {/* Disable LLM parameter selection for enum parameters */}
        {param.enum && param.enum.length > 0 ? (
          <div className="p-3 bg-purple-50 border border-purple-200 rounded-md">
            <p className="text-sm text-purple-800">
              <strong>Enum Parameter</strong>
            </p>
            <p className="text-xs text-purple-600 mt-1">
              Enum parameters must be set to one of the predefined values and cannot use LLM variables.
            </p>
          </div>
        ) : (
          <Select
            value={
              isParameterLLMFilled(param) ? param.value || "" : "direct_input"
            }
            onValueChange={(value) => onUpdateParameterValue(index, value)}
          >
            <SelectTrigger
              className={
                param.required && !param.value?.trim()
                  ? "border-red-300 focus:border-red-500"
                  : ""
              }
            >
              <SelectValue
                placeholder={t("form.tool.parameter_value_placeholder")}
              />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="direct_input">
                <div className="flex items-center gap-2">
                  <span>{t("form.tool.direct_input")}</span>
                  <span className="text-xs text-gray-500">
                    {t("form.tool.direct_input_description")}
                  </span>
                </div>
              </SelectItem>
              {(formData.toolCallConfig?.llmParameters || []).map(
                (llmParam, llmIndex) => (
                  <SelectItem
                    key={llmIndex}
                    value={
                      llmParam.name || t("form.tool.llm_variable.name_empty")
                    }
                  >
                    <div className="flex items-center gap-2">
                      <Bot className="h-3 w-3 text-blue-600" />
                      <span>
                        {llmParam.name ||
                          t("form.tool.llm_variable.name_empty")}
                      </span>
                      <ArrowRight className="h-3 w-3 text-gray-400" />
                      <span className="text-xs text-gray-500">
                        {llmParam.type}
                      </span>
                    </div>
                  </SelectItem>
                )
              )}
            </SelectContent>
          </Select>
        )}

        {!isParameterLLMFilled(param) && (
          <div className="mt-2">
            {/* Handle enum parameters with dropdown */}
            {param.enum && param.enum.length > 0 ? (
              <Select
                value={param.value || ""}
                onValueChange={(value) => onUpdateParameterValue(index, value)}
              >
                <SelectTrigger
                  className={
                    param.required && !param.value?.trim()
                      ? "border-red-300 focus:border-red-500"
                      : ""
                  }
                >
                  <SelectValue placeholder={`Select ${param.name} value`} />
                </SelectTrigger>
                <SelectContent>
                  {param.enum.map((enumValue) => (
                    <SelectItem key={enumValue} value={enumValue}>
                      {enumValue}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            ) : param.type === "object" ? (
              /* Code editor for object parameters */
              <CodeEditor
                value={param.value || ""}
                onChange={(value: string) =>
                  onUpdateParameterValue(index, value)
                }
                placeholder={t("form.tool.enter_parameter_value_template")}
                language="json"
                className={
                  param.required && !param.value?.trim()
                    ? "border-red-300 focus:border-red-500"
                    : ""
                }
              />
            ) : (
              /* Regular textarea for non-enum parameters */
              <Textarea
                value={param.value || ""}
                onChange={(e) => onUpdateParameterValue(index, e.target.value)}
                placeholder={t("form.tool.enter_parameter_value_template")}
                className={`min-h-20 font-mono text-sm ${
                  param.required && !param.value?.trim()
                    ? "border-red-300 focus:border-red-500"
                    : ""
                }`}
              />
            )}
            
            {/* LLM Variables helper - only show for non-enum parameters */}
            {!param.enum &&
              (formData.toolCallConfig?.llmParameters || []).length > 0 && (
                <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded-md">
                  <div className="text-xs font-medium text-blue-800 mb-1">
                    Available LLM Variables:
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {(formData.toolCallConfig?.llmParameters || []).map(
                      (llmParam, llmIndex) => (
                        <code
                          key={llmIndex}
                          className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs cursor-pointer hover:bg-blue-200"
                          onClick={() => {
                            const textarea = document.querySelector(
                              `textarea[value="${param.value || ""}"]`
                            ) as HTMLTextAreaElement
                            if (textarea) {
                              const cursorPos = textarea.selectionStart
                              const textBefore =
                                param.value?.substring(0, cursorPos) || ""
                              const textAfter =
                                param.value?.substring(cursorPos) || ""
                              const newValue =
                                textBefore +
                                `{{llmParam.${llmParam.name}}}` +
                                textAfter
                              onUpdateParameterValue(index, newValue)
                            }
                          }}
                        >
                          {`{{llmParam.${llmParam.name || "unnamed"}}}`}
                        </code>
                      )
                    )}
                  </div>
                </div>
              )}
          </div>
        )}

        {isParameterLLMFilled(param) && (
          <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded-md">
            <div className="flex items-center gap-2 text-sm text-blue-800">
              <Bot className="h-4 w-4" />
              <span>
                {t("form.tool.llm_variable_selected")}{" "}
                <strong>{param.value}</strong>
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
