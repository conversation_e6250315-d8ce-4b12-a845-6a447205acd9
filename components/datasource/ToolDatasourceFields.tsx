"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

import {
  Plus,
  Trash2,
  Play,
  Bot,
  Settings,
  ArrowRight,
  Database,
  Table,
  FileSpreadsheet,
} from "lucide-react"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "./locales"
import { DatasourceFormData } from "./DatasourceFormBase"
import TestDatasourceDialog from "./TestDatasourceDialog"
import { DatasourcesAPI, AvailableTool } from "@/lib/services/datasourcesApi"
import { LLMParameter } from "@/lib/repositories/datasources/interface"

interface ToolDatasourceFieldsProps {
  formData: DatasourceFormData
  onFormDataChange: (data: DatasourceFormData) => void
  datasourceId?: string // Optional for existing datasources
}

export default function ToolDatasourceFields({
  formData,
  onFormDataChange,
  datasourceId,
}: ToolDatasourceFieldsProps) {
  const { t } = useLocalization("datasource", locales)
  const [showTestDialog, setShowTestDialog] = useState(false)
  const [availableTools, setAvailableTools] = useState<AvailableTool[]>([])
  const [loadingTools, setLoadingTools] = useState(false)
  const [isCustomTool, setIsCustomTool] = useState(false)
  // Fetch available tools on component mount
  useEffect(() => {
    const fetchAvailableTools = async () => {
      try {
        setLoadingTools(true)
        const response = await DatasourcesAPI.GetAvailableTools().request()
        setAvailableTools(response)
        console.log("TOOLNAME", formData.toolCallConfig?.toolId, response)
        // Check if current tool is a custom tool (not in prebuilt list)
        if (formData.toolCallConfig?.toolId) {
          const isPrebuilt = response.some(
            (tool) => tool.id === formData.toolCallConfig?.toolId,
          )
          setIsCustomTool(!isPrebuilt)
        }
      } catch (error) {
        console.error("Failed to fetch available tools:", error)
      } finally {
        setLoadingTools(false)
      }
    }

    fetchAvailableTools()
  }, [])

  const updateFormData = (updates: Partial<DatasourceFormData>) => {
    onFormDataChange({ ...formData, ...updates })
  }

  const updateToolConfig = (
    updates: Partial<NonNullable<DatasourceFormData["toolCallConfig"]>>,
  ) => {
    updateFormData({
      toolCallConfig: {
        ...formData.toolCallConfig!,
        ...updates,
      },
    })
  }

  // Handle tool selection and update parameters template
  const handleToolSelection = (toolId: string) => {
    if (toolId === "custom") {
      setIsCustomTool(true)
      updateToolConfig({
        toolId: "",
        parameters: [],
        llmParameters: [],
      })
      return
    }

    setIsCustomTool(false)
    const selectedTool = availableTools.find((tool) => tool.id === toolId)

    if (selectedTool) {
      // Convert tool parameters to form parameters
      const formParameters = selectedTool.parameters.map((param) => ({
        name: param.name,
        type: param.type,
        required: param.required,
        description: param.description || "",
        value: "",
      }))

      updateToolConfig({
        toolId: selectedTool.id,
        description: selectedTool.description,
        parameters: formParameters,
        llmParameters: [],
      })
    }
  }

  // LLM Parameter management functions
  const addLLMParameter = () => {
    const newLLMParameter: LLMParameter = {
      name: "",
      type: "string",
      description: "",
      prompt: "",
    }

    updateToolConfig({
      llmParameters: [
        ...(formData.toolCallConfig?.llmParameters || []),
        newLLMParameter,
      ],
    })
  }

  const removeLLMParameter = (index: number) => {
    const llmParameters = [...(formData.toolCallConfig?.llmParameters || [])]
    llmParameters.splice(index, 1)
    updateToolConfig({ llmParameters })
  }

  const updateLLMParameter = (
    index: number,
    updates: Partial<LLMParameter>,
  ) => {
    const llmParameters = [...(formData.toolCallConfig?.llmParameters || [])]
    llmParameters[index] = { ...llmParameters[index], ...updates }
    updateToolConfig({ llmParameters })
  }

  // Tool Parameter management functions (updated to handle LLM mapping)
  const updateToolParameter = (
    index: number,
    updates: Partial<
      NonNullable<DatasourceFormData["toolCallConfig"]>["parameters"][0]
    >,
  ) => {
    const parameters = [...(formData.toolCallConfig?.parameters || [])]
    parameters[index] = { ...parameters[index], ...updates }
    updateToolConfig({ parameters })
  }

  const updateParameterValue = (paramIndex: number, value: string) => {
    updateToolParameter(paramIndex, {
      value: value === "direct_input" ? "" : value,
    })
  }

  // Helper function to check if a parameter is LLM-filled
  const isParameterLLMFilled = (param: any) => {
    return (
      param.value &&
      (formData.toolCallConfig?.llmParameters || []).some(
        (llmParam) => llmParam.name === param.value,
      )
    )
  }

  const addParameter = () => {
    updateToolConfig({
      parameters: [
        ...(formData.toolCallConfig?.parameters || []),
        {
          name: "",
          type: "string",
          description: "",
          required: false,
          value: "",
        },
      ],
    })
  }

  const removeParameter = (index: number) => {
    updateToolConfig({
      parameters:
        formData.toolCallConfig?.parameters?.filter((_, i) => i !== index) ||
        [],
    })
  }

  const canTest =
    datasourceId &&
    formData.toolCallConfig?.toolId?.trim() &&
    formData.toolCallConfig?.description?.trim()

  const handleTestTool = () => {
    setShowTestDialog(true)
  }

  // Helper function to get icon for tool type
  const getToolIcon = (toolId: string) => {
    switch (toolId) {
      case "google_sheets_reader":
        return FileSpreadsheet
      case "mongodb_reader":
        return Database
      case "postgresql_reader":
        return Table
      case "api_caller":
        return Settings
      default:
        return Settings
    }
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>{t("form.tool.title")}</CardTitle>
              <CardDescription>{t("form.tool.description")}</CardDescription>
            </div>
            {canTest && (
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleTestTool}
                className="flex items-center gap-2"
              >
                <Play className="h-4 w-4" />
                {t("form.buttons.try")}
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Query Field */}
          <div className="space-y-2">
            <Label htmlFor="query">{t("form.tool.query_label")}</Label>
            <Textarea
              id="query"
              value={formData.query || ""}
              onChange={(e) => updateFormData({ query: e.target.value })}
              placeholder={t("form.tool.query_placeholder")}
              className="min-h-20"
              maxLength={1000}
              required
            />
            <p className="text-sm text-gray-500">
              {(formData.query || "").length}/1,000{" "}
              {t("form.common.characters")}
            </p>
          </div>

          {/* Tool Name */}
          <div className="space-y-2">
            <Label htmlFor="toolId">{t("form.tool.tool_name_label")}</Label>
            {loadingTools ? (
              <div className="flex items-center justify-center p-3 border rounded-md">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-900"></div>
                <span className="ml-2 text-sm text-gray-500">
                  {t("form.tool.loading_tools")}
                </span>
              </div>
            ) : (
              <>
                <Select
                  value={
                    isCustomTool
                      ? "custom"
                      : formData.toolCallConfig?.toolId || ""
                  }
                  onValueChange={handleToolSelection}
                >
                  <SelectTrigger>
                    <SelectValue
                      placeholder={t("form.tool.tool_name_placeholder")}
                    />
                  </SelectTrigger>
                  <SelectContent>
                    {availableTools.map((tool) => {
                      const IconComponent = getToolIcon(tool.id)
                      return (
                        <SelectItem key={tool.id} value={tool.id}>
                          <div className="flex items-center gap-2">
                            <IconComponent className="h-4 w-4 text-gray-600" />
                            <div className="flex flex-col">
                              <span className="font-medium">{tool.name}</span>
                              <span className="text-xs text-gray-500">
                                {tool.description}
                              </span>
                            </div>
                          </div>
                        </SelectItem>
                      )
                    })}
                    <SelectItem value="custom">
                      <div className="flex flex-col">
                        <span className="font-medium">
                          {t("form.tool.custom_tool_option")}
                        </span>
                        <span className="text-xs text-gray-500">
                          {t("form.tool.custom_tool_description")}
                        </span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>

                {isCustomTool && (
                  <Input
                    value={formData.toolCallConfig?.toolId || ""}
                    onChange={(e) =>
                      updateToolConfig({ toolId: e.target.value })
                    }
                    placeholder={t("form.tool.tool_name_custom_placeholder")}
                    className="mt-2"
                    required
                  />
                )}

                {!isCustomTool && formData.toolCallConfig?.toolId && (
                  <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-md">
                    <p className="text-sm text-blue-800">
                      <strong>{t("form.tool.prebuilt_tool_selected")}</strong>
                    </p>
                  </div>
                )}
              </>
            )}
          </div>

          {/* Tool Description */}
          <div className="space-y-2">
            <Label htmlFor="toolDescription">
              {t("form.tool.tool_description_label")}
              {!isCustomTool && formData.toolCallConfig?.toolId && (
                <span className="text-xs text-gray-500 ml-2">
                  {t("form.tool.auto_populated_description")}
                </span>
              )}
            </Label>
            <Textarea
              id="toolDescription"
              value={formData.toolCallConfig?.description || ""}
              onChange={(e) =>
                updateToolConfig({ description: e.target.value })
              }
              placeholder={t("form.tool.tool_description_placeholder")}
              className="min-h-20"
              required
            />
          </div>

          {/* Parameters Section with Tabs */}
          <div className="space-y-4">
            <Label className="text-base font-semibold">
              {t("form.tool.parameters_label")}
              {!isCustomTool && formData.toolCallConfig?.toolId && (
                <span className="text-xs text-gray-500 ml-2">
                  {t("form.tool.auto_configured_parameters")}
                </span>
              )}
            </Label>

            <Tabs defaultValue="tool-params" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger
                  value="tool-params"
                  className="flex items-center gap-2"
                >
                  <Settings className="h-4 w-4" />
                  {t("form.tool.tool_parameters_tab")}
                </TabsTrigger>
                <TabsTrigger
                  value="llm-params"
                  className="flex items-center gap-2"
                >
                  <Bot className="h-4 w-4" />
                  {t("form.tool.llm_parameters_tab")}
                </TabsTrigger>
              </TabsList>

              {/* Tool Parameters Tab */}
              <TabsContent value="tool-params" className="space-y-4">
                <div className="flex items-center justify-between">
                  <p className="text-sm text-gray-600">
                    {t("form.tool.tool_parameters_description")}
                  </p>
                  {isCustomTool && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={addParameter}
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      {t("form.tool.add_parameter")}
                    </Button>
                  )}
                </div>

                {!isCustomTool &&
                  formData.toolCallConfig?.toolId &&
                  (formData.toolCallConfig?.parameters || []).length > 0 && (
                    <div className="mb-3 p-3 bg-green-50 border border-green-200 rounded-md">
                      <p className="text-sm text-green-800">
                        <strong>
                          {t("form.tool.parameters_auto_configured")}
                        </strong>
                      </p>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={addParameter}
                        className="mt-2"
                      >
                        <Plus className="h-4 w-4 mr-1" />
                        {t("form.tool.add_custom_parameter")}
                      </Button>
                    </div>
                  )}

                {(formData.toolCallConfig?.parameters || []).map(
                  (param, index) => (
                    <div
                      key={index}
                      className="border rounded-lg p-4 space-y-3"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <h4 className="font-medium">
                            {param.name || `Parameter ${index + 1}`}
                          </h4>
                          {param.required && (
                            <span className="px-2 py-1 bg-red-100 text-red-800 rounded-md text-xs font-medium">
                              Required
                            </span>
                          )}
                          {param.enum && param.enum.length > 0 && (
                            <span className="px-2 py-1 bg-purple-100 text-purple-800 rounded-md text-xs font-medium">
                              Enum
                            </span>
                          )}
                          {isParameterLLMFilled(param) && (
                            <div className="flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 rounded-md text-xs">
                              <Bot className="h-3 w-3" />
                              LLM
                            </div>
                          )}
                        </div>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeParameter(index)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>

                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <Label className="text-sm font-medium text-gray-700">
                            {t("form.tool.variable_name")}
                          </Label>
                          <p className="text-sm text-gray-900 font-mono px-3 py-2">
                            {param.name}
                          </p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-700">
                            {t("form.tool.type")}
                          </Label>
                          <p className="text-sm text-gray-900 capitalize px-3 py-2">
                            {param.type}
                          </p>
                        </div>
                      </div>

                      <div>
                        <Label className="text-sm font-medium text-gray-700">
                          {t("form.tool.description")}
                        </Label>
                        <p className="text-sm text-gray-900 px-3 py-2 min-h-16 whitespace-pre-wrap">
                          {param.description || "No description provided"}
                        </p>
                        {param.enum && param.enum.length > 0 && (
                          <p className="text-xs text-purple-600 mt-1">
                            Available values: {param.enum.join(", ")}
                          </p>
                        )}
                      </div>

                      <div>
                        <div className="flex items-center gap-2">
                          <Label>{t("form.tool.parameter_value")}</Label>
                          {param.required && !param.value?.trim() && (
                            <span className="text-xs text-red-600 font-medium">
                              ⚠️ Required field is empty
                            </span>
                          )}
                        </div>
                        {/* Disable LLM parameter selection for enum parameters */}
                        {param.enum && param.enum.length > 0 ? (
                          <div className="p-3 bg-purple-50 border border-purple-200 rounded-md">
                            <p className="text-sm text-purple-800">
                              <strong>Enum Parameter</strong>
                            </p>
                            <p className="text-xs text-purple-600 mt-1">
                              Enum parameters must be set to one of the
                              predefined values and cannot use LLM variables.
                            </p>
                          </div>
                        ) : (
                          <Select
                            value={
                              isParameterLLMFilled(param)
                                ? param.value || ""
                                : "direct_input"
                            }
                            onValueChange={(value) =>
                              updateParameterValue(index, value)
                            }
                          >
                            <SelectTrigger
                              className={
                                param.required && !param.value?.trim()
                                  ? "border-red-300 focus:border-red-500"
                                  : ""
                              }
                            >
                              <SelectValue
                                placeholder={t(
                                  "form.tool.parameter_value_placeholder",
                                )}
                              />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="direct_input">
                                <div className="flex items-center gap-2">
                                  <span>{t("form.tool.direct_input")}</span>
                                  <span className="text-xs text-gray-500">
                                    {t("form.tool.direct_input_description")}
                                  </span>
                                </div>
                              </SelectItem>
                              {(
                                formData.toolCallConfig?.llmParameters || []
                              ).map((llmParam, llmIndex) => (
                                <SelectItem
                                  key={llmIndex}
                                  value={
                                    llmParam.name ||
                                    t("form.tool.llm_variable.name_empty")
                                  }
                                >
                                  <div className="flex items-center gap-2">
                                    <Bot className="h-3 w-3 text-blue-600" />
                                    <span>
                                      {llmParam.name ||
                                        t("form.tool.llm_variable.name_empty")}
                                    </span>
                                    <ArrowRight className="h-3 w-3 text-gray-400" />
                                    <span className="text-xs text-gray-500">
                                      {llmParam.type}
                                    </span>
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        )}

                        {!isParameterLLMFilled(param) && (
                          <div className="mt-2">
                            {/* Handle enum parameters with dropdown */}
                            {param.enum && param.enum.length > 0 ? (
                              <Select
                                value={param.value || ""}
                                onValueChange={(value) =>
                                  updateParameterValue(index, value)
                                }
                              >
                                <SelectTrigger
                                  className={
                                    param.required && !param.value?.trim()
                                      ? "border-red-300 focus:border-red-500"
                                      : ""
                                  }
                                >
                                  <SelectValue
                                    placeholder={`Select ${param.name} value`}
                                  />
                                </SelectTrigger>
                                <SelectContent>
                                  {param.enum.map((enumValue) => (
                                    <SelectItem
                                      key={enumValue}
                                      value={enumValue}
                                    >
                                      {enumValue}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            ) : (
                              /* Regular textarea for non-enum parameters */
                              <Textarea
                                value={param.value || ""}
                                onChange={(e) =>
                                  updateParameterValue(index, e.target.value)
                                }
                                placeholder={t(
                                  "form.tool.enter_parameter_value_template",
                                )}
                                className={`min-h-20 font-mono text-sm ${param.required && !param.value?.trim() ? "border-red-300 focus:border-red-500" : ""}`}
                              />
                            )}
                            {/* LLM Variables helper - only show for non-enum parameters */}
                            {!param.enum &&
                              (formData.toolCallConfig?.llmParameters || [])
                                .length > 0 && (
                                <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded-md">
                                  <div className="text-xs font-medium text-blue-800 mb-1">
                                    Available LLM Variables:
                                  </div>
                                  <div className="flex flex-wrap gap-1">
                                    {(
                                      formData.toolCallConfig?.llmParameters ||
                                      []
                                    ).map((llmParam, llmIndex) => (
                                      <code
                                        key={llmIndex}
                                        className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs cursor-pointer hover:bg-blue-200"
                                        onClick={() => {
                                          const textarea =
                                            document.querySelector(
                                              `textarea[value="${param.value || ""}"]`,
                                            ) as HTMLTextAreaElement
                                          if (textarea) {
                                            const cursorPos =
                                              textarea.selectionStart
                                            const textBefore =
                                              param.value?.substring(
                                                0,
                                                cursorPos,
                                              ) || ""
                                            const textAfter =
                                              param.value?.substring(
                                                cursorPos,
                                              ) || ""
                                            const newValue =
                                              textBefore +
                                              `{{llmParam.${llmParam.name}}}` +
                                              textAfter
                                            updateParameterValue(
                                              index,
                                              newValue,
                                            )
                                          }
                                        }}
                                      >
                                        {`{{llmParam.${llmParam.name || "unnamed"}}}`}
                                      </code>
                                    ))}
                                  </div>
                                </div>
                              )}
                          </div>
                        )}

                        {isParameterLLMFilled(param) && (
                          <div className="mt-2 p-2 bg-blue-50 border border-blue-200 rounded-md">
                            <div className="flex items-center gap-2 text-sm text-blue-800">
                              <Bot className="h-4 w-4" />
                              <span>
                                {t("form.tool.llm_variable_selected")}{" "}
                                <strong>{param.value}</strong>
                              </span>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  ),
                )}

                {(formData.toolCallConfig?.parameters || []).length === 0 && (
                  <p className="text-gray-500 text-center py-8">
                    {t("form.tool.no_parameters")}
                  </p>
                )}
              </TabsContent>

              {/* LLM Parameters Tab */}
              <TabsContent value="llm-params" className="space-y-4">
                <div className="flex items-center justify-between">
                  <p className="text-sm text-gray-600">
                    {t("form.tool.llm_parameters_description")}
                  </p>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addLLMParameter}
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    {t("form.tool.add_llm_parameter")}
                  </Button>
                </div>

                {(formData.toolCallConfig?.llmParameters || []).map(
                  (llmParam, index) => (
                    <div
                      key={index}
                      className="border rounded-lg p-4 space-y-3 bg-blue-50 border-blue-200"
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Bot className="h-4 w-4 text-blue-600" />
                          <h4 className="font-medium text-blue-800">
                            LLM Parameter {index + 1}
                          </h4>
                        </div>
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => removeLLMParameter(index)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>

                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <Label>{t("form.tool.variable_name")}</Label>
                          <Input
                            value={llmParam.name}
                            onChange={(e) =>
                              updateLLMParameter(index, {
                                name: e.target.value,
                              })
                            }
                            placeholder="order_number"
                          />
                        </div>
                        <div>
                          <Label>Type</Label>
                          <Select
                            value={llmParam.type}
                            onValueChange={(value) =>
                              updateLLMParameter(index, { type: value as any })
                            }
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="string">String</SelectItem>
                              <SelectItem value="number">Number</SelectItem>
                              <SelectItem value="boolean">Boolean</SelectItem>
                              <SelectItem value="object">Object</SelectItem>
                              <SelectItem value="array">Array</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <div>
                        <Label>Description</Label>
                        <Textarea
                          value={llmParam.description}
                          onChange={(e) =>
                            updateLLMParameter(index, {
                              description: e.target.value,
                            })
                          }
                          placeholder="Describe what this parameter represents..."
                          className="min-h-16"
                        />
                      </div>

                      <div>
                        <Label>{t("form.tool.llm_prompt")}</Label>
                        <Textarea
                          value={llmParam.prompt || ""}
                          onChange={(e) =>
                            updateLLMParameter(index, {
                              prompt: e.target.value,
                            })
                          }
                          placeholder={t("form.tool.llm_prompt_placeholder")}
                          className="min-h-20"
                        />
                      </div>
                    </div>
                  ),
                )}

                {(formData.toolCallConfig?.llmParameters || []).length ===
                  0 && (
                  <p className="text-gray-500 text-center py-8">
                    {t("form.tool.no_llm_parameters_message")}
                  </p>
                )}
              </TabsContent>
            </Tabs>
          </div>
        </CardContent>
      </Card>

      {/* Test Dialog */}
      {canTest && (
        <TestDatasourceDialog
          open={showTestDialog}
          formData={formData}
          onOpenChange={setShowTestDialog}
          datasourceId={datasourceId!}
          toolId={formData.toolCallConfig?.toolId || ""}
          description={formData.toolCallConfig?.description || ""}
          parameters={formData.toolCallConfig?.parameters || []}
        />
      )}
    </>
  )
}

export function validateToolDatasource(formData: DatasourceFormData): boolean {
  const basicValidation = !!(
    formData.name.trim() &&
    formData.query?.trim() &&
    formData.toolCallConfig?.toolId?.trim() &&
    formData.toolCallConfig?.description?.trim()
  )

  if (!basicValidation) return false

  // Validate LLM parameter mappings
  const toolParams = formData.toolCallConfig?.parameters || []
  const llmParams = formData.toolCallConfig?.llmParameters || []
  const llmParamNames = llmParams.map((p) => p.name)

  // Check that all LLM-filled parameters have valid LLM variable mappings
  for (const param of toolParams) {
    const isLLMFilled = param.value && llmParamNames.includes(param.value)

    if (isLLMFilled && !llmParamNames.includes(param.value!)) {
      return false // LLM variable doesn't exist
    }

    // Check that required parameters have values (either direct or LLM)
    if (param.required && !param.value?.trim()) {
      return false // Required parameter has no value
    }
  }

  // Check that all LLM parameters have names and descriptions
  for (const llmParam of llmParams) {
    if (!llmParam.name?.trim() || !llmParam.description?.trim()) {
      return false // LLM parameter missing required fields
    }

    // Ensure LLM parameter names are not empty strings
    if (llmParam.name.trim() === "") {
      return false // LLM parameter name cannot be empty
    }
  }

  return true
}
