"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"

import { Plus, Play, Bot, Settings } from "lucide-react"
import { useLocalization } from "@/localization/functions/client"
import { locales } from "./locales"
import { DatasourceFormData } from "./DatasourceFormBase"
import TestDatasourceDialog from "./TestDatasourceDialog"
import { ToolSelectionSection } from "./ToolSelectionSection"
import { ToolParameterCard } from "./ToolParameterCard"
import { LLMParameterCard } from "./LLMParameterCard"
import { useToolDatasourceLogic } from "./useToolDatasourceLogic"
export { validateToolDatasource } from "./ToolDatasourceValidation"

interface ToolDatasourceFieldsProps {
  formData: DatasourceFormData
  onFormDataChange: (data: DatasourceFormData) => void
  datasourceId?: string // Optional for existing datasources
}

export default function ToolDatasourceFields({
  formData,
  onFormDataChange,
  datasourceId,
}: ToolDatasourceFieldsProps) {
  const { t } = useLocalization("datasource", locales)

  const {
    showTestDialog,
    setShowTestDialog,
    availableTools,
    loadingTools,
    isCustomTool,
    updateFormData,
    updateToolConfig,
    handleToolSelection,
    addLLMParameter,
    removeLLMParameter,
    updateLLMParameter,
    updateParameterValue,
    isParameterLLMFilled,
    addParameter,
    removeParameter,
    canTest,
    handleTestTool,
  } = useToolDatasourceLogic(formData, onFormDataChange, datasourceId)

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>{t("form.tool.title")}</CardTitle>
              <CardDescription>{t("form.tool.description")}</CardDescription>
            </div>
            {canTest && (
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleTestTool}
                className="flex items-center gap-2"
              >
                <Play className="h-4 w-4" />
                {t("form.buttons.try")}
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <ToolSelectionSection
            formData={formData}
            availableTools={availableTools}
            loadingTools={loadingTools}
            isCustomTool={isCustomTool}
            onToolSelection={handleToolSelection}
            onUpdateToolConfig={updateToolConfig}
            onUpdateFormData={updateFormData}
          />

          {/* Parameters Section with Tabs */}
          <div className="space-y-4">
            <Label className="text-base font-semibold">
              {t("form.tool.parameters_label")}
              {!isCustomTool && formData.toolCallConfig?.toolId && (
                <span className="text-xs text-gray-500 ml-2">
                  {t("form.tool.auto_configured_parameters")}
                </span>
              )}
            </Label>

            <Tabs defaultValue="tool-params" className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger
                  value="tool-params"
                  className="flex items-center gap-2"
                >
                  <Settings className="h-4 w-4" />
                  {t("form.tool.tool_parameters_tab")}
                </TabsTrigger>
                <TabsTrigger
                  value="llm-params"
                  className="flex items-center gap-2"
                >
                  <Bot className="h-4 w-4" />
                  {t("form.tool.llm_parameters_tab")}
                </TabsTrigger>
              </TabsList>

              {/* Tool Parameters Tab */}
              <TabsContent value="tool-params" className="space-y-4">
                <div className="flex items-center justify-between">
                  <p className="text-sm text-gray-600">
                    {t("form.tool.tool_parameters_description")}
                  </p>
                  {isCustomTool && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={addParameter}
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      {t("form.tool.add_parameter")}
                    </Button>
                  )}
                </div>

                {!isCustomTool &&
                  formData.toolCallConfig?.toolId &&
                  (formData.toolCallConfig?.parameters || []).length > 0 && (
                    <div className="mb-3 p-3 bg-green-50 border border-green-200 rounded-md">
                      <p className="text-sm text-green-800">
                        <strong>
                          {t("form.tool.parameters_auto_configured")}
                        </strong>
                      </p>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={addParameter}
                        className="mt-2"
                      >
                        <Plus className="h-4 w-4 mr-1" />
                        {t("form.tool.add_custom_parameter")}
                      </Button>
                    </div>
                  )}

                {(formData.toolCallConfig?.parameters || []).map(
                  (param, index) => (
                    <ToolParameterCard
                      key={index}
                      param={param}
                      index={index}
                      formData={formData}
                      onRemoveParameter={removeParameter}
                      onUpdateParameterValue={updateParameterValue}
                      isParameterLLMFilled={isParameterLLMFilled}
                    />
                  ),
                )}

                {(formData.toolCallConfig?.parameters || []).length === 0 && (
                  <p className="text-gray-500 text-center py-8">
                    {t("form.tool.no_parameters")}
                  </p>
                )}
              </TabsContent>

              {/* LLM Parameters Tab */}
              <TabsContent value="llm-params" className="space-y-4">
                <div className="flex items-center justify-between">
                  <p className="text-sm text-gray-600">
                    {t("form.tool.llm_parameters_description")}
                  </p>
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={addLLMParameter}
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    {t("form.tool.add_llm_parameter")}
                  </Button>
                </div>

                {(formData.toolCallConfig?.llmParameters || []).map(
                  (llmParam, index) => (
                    <LLMParameterCard
                      key={index}
                      llmParam={llmParam}
                      index={index}
                      onRemoveLLMParameter={removeLLMParameter}
                      onUpdateLLMParameter={updateLLMParameter}
                    />
                  ),
                )}

                {(formData.toolCallConfig?.llmParameters || []).length ===
                  0 && (
                  <p className="text-gray-500 text-center py-8">
                    {t("form.tool.no_llm_parameters_message")}
                  </p>
                )}
              </TabsContent>
            </Tabs>
          </div>
        </CardContent>
      </Card>

      {/* Test Dialog */}
      {canTest && (
        <TestDatasourceDialog
          open={showTestDialog}
          formData={formData}
          onOpenChange={setShowTestDialog}
          datasourceId={datasourceId!}
          toolId={formData.toolCallConfig?.toolId || ""}
          description={formData.toolCallConfig?.description || ""}
          parameters={formData.toolCallConfig?.parameters || []}
        />
      )}
    </>
  )
}
